import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService, FlowProcessStep } from '@core/database';

@Injectable()
export class GetAllFlowProcessStepsUseCase implements UseCase {
  private readonly logger = new Logger(GetAllFlowProcessStepsUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ processId }: { processId: EntityId }): Promise<FlowProcessStep[]> {
    this.logger.verbose({ msg: `Started getting all steps (process id: ${processId})` });

    const steps = await this.db.flowProcessSteps.find({
      where: {
        flowProcess: { id: processId },
      },
    });

    this.logger.verbose({
      msg: `All steps found`,
      data: {
        incomingData: { processId },
        stepsFound: steps,
      },
    });

    return steps;
  }
}
