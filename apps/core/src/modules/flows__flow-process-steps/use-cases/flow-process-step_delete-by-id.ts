import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Like } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';

@Injectable()
export class DeleteFlowProcessStepByIdUseCase implements UseCase {
  private readonly logger = new Logger(DeleteFlowProcessStepByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ processId, stepId }: { processId: EntityId; stepId: EntityId }): Promise<FlowProcessStep> {
    this.logger.verbose({ msg: `Started deleting step (step id: ${stepId}, process id: ${processId})` });

    const [deletedStep, nextStep] = await this.db.flowProcessSteps.manager.transaction(
      async entityManager => {
        this.logger.verbose({ msg: `Finding step to delete` });

        /** Find step to delete */
        const stepToDelete = await entityManager.findOne(FlowProcessStep, {
          where: { id: pgId(stepId) },
        });

        if (!stepToDelete)
          throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

        this.logger.verbose({ msg: `Step found`, data: stepToDelete });

        this.logger.verbose({ msg: `Deleting step` });

        /** Delete step */
        await entityManager.delete(FlowProcessStep, stepToDelete.id);

        this.logger.verbose({ msg: `Step deleted` });

        this.logger.verbose({ msg: `Deleting children (mPath: ${stepToDelete.mPath}/%)` });

        /** Delete children */
        await entityManager.delete(FlowProcessStep, {
          flowProcess: { id: processId },
          mPath: Like(`${stepToDelete.mPath}/%`),
        });

        this.logger.verbose({ msg: `Children deleted` });

        this.logger.verbose({ msg: `Finding next step to update prevId (prevId: ${stepToDelete.id})` });

        /** Update next step prevId */
        const nextStep = await entityManager.findOne(FlowProcessStep, {
          where: {
            flowProcess: { id: processId },
            prevId: stepToDelete.id,
          },
        });

        if (nextStep) {
          this.logger.verbose({ msg: `Next step found`, data: nextStep });

          this.logger.verbose({ msg: `Updating next step prevId (new prevId: ${stepToDelete.prevId})` });

          nextStep.prevId = stepToDelete.prevId;
          await entityManager.save(FlowProcessStep, nextStep);
        } else {
          this.logger.verbose({ msg: `No next step found` });
        }

        return [stepToDelete, nextStep];
      },
    );

    this.logger.log({
      msg: `Step deleted with its children`,
      data: {
        incomingData: { processId, stepId },
        deletedStep,
        modifiedData: { nextStep },
      },
    });

    return deletedStep;
  }
}
