import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNull, Like } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';
import { MoveFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class MoveFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger('MoveFlowProcessStep');

  constructor(
    private readonly db: DatabaseService,
    private readonly stepService: FlowProcessStepsService,
  ) {}

  async execute(id: EntityId, processId: EntityId, dto: MoveFlowProcessStepDto): Promise<FlowProcessStep> {
    /** This is a data with a new destination of the moving step  */
    const { parentId: targetParentId, prevId: targetPrevId } = dto;

    this.logger.verbose({ msg: `Started moving step (step id: ${id})`, data: dto });

    // TODO: optimize

    const [movingStep, nextStep, targetNextStep, targetParentStep] =
      await this.db.flowProcessSteps.manager.transaction(async entityManager => {
        this.logger.verbose({ msg: 'Finding data' });

        // TODO: find many and then use find by result array

        const [movingStep, nextStep, targetNextStep, targetParentStep] = await Promise.all([
          /** Moving step */
          entityManager.findOne(FlowProcessStep, {
            where: { id: pgId(id) },
          }),

          /** Next step (it is the step with prevId = movingStep.id) */
          entityManager.findOne(FlowProcessStep, {
            where: { flowProcess: { id: pgId(processId) }, prevId: pgId(id) },
          }),

          /** Target next step (it is the step with prevId = targetPrevId) */
          /** We don't need the target step itself, we just need to update next step's prevId */
          entityManager.findOne(FlowProcessStep, {
            where: {
              flowProcess: { id: pgId(processId) },
              parentId: targetParentId ? pgId(targetParentId) : IsNull(),
              prevId: targetPrevId ? pgId(targetPrevId) : IsNull(),
            },
          }),
          /** Target parent step */
          targetParentId
            ? entityManager.findOne(FlowProcessStep, {
                where: { id: pgId(targetParentId) },
              })
            : null,
        ]);

        this.logger.verbose({
          msg: 'Data found',
          data: { step: movingStep, nextStep, targetNextStep, targetParentStep },
        });

        if (!movingStep)
          throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

        /** Shift nextStep above */
        if (nextStep) {
          this.logger.verbose({ msg: 'Next step found' });

          this.logger.verbose({
            msg: `Shifting nextStep (id: ${nextStep.id}) above (prevId: ${movingStep.prevId})`,
          });

          nextStep.prevId = movingStep.prevId;
          await entityManager.save(FlowProcessStep, nextStep);
        } else {
          this.logger.verbose({ msg: 'No next step found' });
        }

        /** Move step */
        const newStepMPath = await this.stepService.generateUniqueMaterializedPath(entityManager, {
          processId,
          parentMPath: targetParentStep ? targetParentStep.mPath : undefined,
        });

        this.logger.verbose({
          msg: `Moving step (id: ${movingStep.id}, mPath: ${newStepMPath}) to new parent (parentId: ${targetParentId}) and prev (prevId: ${targetPrevId})`,
        });

        const currentStepMPath = movingStep.mPath;

        movingStep.parentId = pgId(targetParentId);
        movingStep.prevId = pgId(targetPrevId);
        movingStep.mPath = newStepMPath;

        await entityManager.save(FlowProcessStep, movingStep);

        this.logger.verbose({ msg: 'Step moved' });

        /** Update mPath for children */
        this.logger.verbose({
          msg: `Updating mPath for children (old: ${currentStepMPath}, new: ${newStepMPath})`,
        });

        await entityManager.update(
          FlowProcessStep,
          {
            flowProcess: { id: pgId(processId) },
            mPath: Like(`${currentStepMPath}/%`),
          },
          {
            mPath: newStepMPath,
          },
        );

        /** Shift targetNextStep below */
        if (targetNextStep) {
          this.logger.verbose({ msg: 'Target next step found' });

          this.logger.verbose({
            msg: `Shifting targetStep (id: ${targetNextStep.id}) below (to ${movingStep.id})`,
          });

          targetNextStep.prevId = movingStep.id;
          await entityManager.save(FlowProcessStep, targetNextStep);
        } else {
          this.logger.verbose({ msg: 'No target next step found' });
        }

        return [movingStep, nextStep, targetNextStep, targetParentStep];
      });

    this.logger.log({
      msg: 'Step moved',
      data: {
        incomingData: { id, processId, dto },
        modifiedStep: movingStep,
        modifiedData: { nextStep, targetNextStep, targetParentStep },
      },
    });

    return movingStep;
  }
}
