import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';

@Injectable()
export class GetFlowProcessStepByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowProcessStepByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute(id: EntityId): Promise<FlowProcessStep> {
    this.logger.verbose({ msg: `Started getting step (step id: ${id})` });

    const step = await this.db.flowProcessSteps.findOne({
      where: { id: pgId(id) },
    });

    if (!step) throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    this.logger.verbose({
      msg: `Step found`,
      data: {
        incomingData: { id },
        foundStep: step,
      },
    });

    return step;
  }
}
