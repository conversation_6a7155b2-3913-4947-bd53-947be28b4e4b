import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';
import { PartialUpdateFlowProcessStepDto, UpdateFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class UpdateFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger(UpdateFlowProcessStepUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly stepService: FlowProcessStepsService,
  ) {}

  async execute(
    id: EntityId,
    dto: UpdateFlowProcessStepDto | PartialUpdateFlowProcessStepDto,
  ): Promise<FlowProcessStep> {
    const { name, description } = dto;
    this.logger.verbose({ msg: `Started updating step (step id: ${id})`, data: dto });

    this.logger.verbose({ msg: `Finding step to update` });

    const step = await this.db.flowProcessSteps.findOne({
      where: { id: pgId(id) },
    });

    if (!step) throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    this.logger.verbose({ msg: `Step found`, data: step });

    this.logger.verbose({ msg: `Updating step` });

    const modifiedStep = this.stepService.normalizeEntityValues(step, {
      name,
      description,
    });

    await this.db.flowProcessSteps.save(modifiedStep);

    this.logger.log({
      msg: `Step updated`,
      data: {
        incomingData: { id, dto },
        modifiedStep,
      },
    });

    return modifiedStep;
  }
}
