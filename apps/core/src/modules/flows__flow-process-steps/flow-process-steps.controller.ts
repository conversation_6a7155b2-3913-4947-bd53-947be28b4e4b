import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { z } from 'zod';
import { FlowProcessStepSchema } from '@core/models';
import { ApiSuccessfulResponse, HttpResponse, SanitizeResponseWithZod } from '@libs/common/api';
import {
  CreateFlowProcessStepDto,
  FlowProcessStepDto,
  MoveFlowProcessStepDto,
  PartialUpdateFlowProcessStepDto,
  UpdateFlowProcessStepDto,
} from './flow-process-steps.dto';
import {
  CreateFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
  GetAllFlowProcessStepsUseCase,
  GetFlowProcessStepByIdUseCase,
  MoveFlowProcessStepUseCase,
  UpdateFlowProcessStepUseCase,
} from './use-cases';

const ApiParamFlowProcessStepId = () =>
  ApiParam({ name: 'stepId', required: true, description: 'Flow process step identifier', type: 'string' });

@Controller('flows/:flowId/processes/:processId/steps')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
@ApiParam({ name: 'processId', required: true, description: 'Flow process identifier', type: 'string' })
export class FlowProcessStepsController {
  constructor(
    private readonly createFlowProcessStepUseCase: CreateFlowProcessStepUseCase,
    private readonly updateFlowProcessStepUseCase: UpdateFlowProcessStepUseCase,
    private readonly deleteFlowProcessStepByIdUseCase: DeleteFlowProcessStepByIdUseCase,
    private readonly moveFlowProcessStepUseCase: MoveFlowProcessStepUseCase,
    private readonly getFlowProcessStepByIdUseCase: GetFlowProcessStepByIdUseCase,
    private readonly getAllFlowProcessStepsUseCase: GetAllFlowProcessStepsUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow process step created', FlowProcessStepDto)
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async createFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Body() dto: CreateFlowProcessStepDto,
  ) {
    const step = await this.createFlowProcessStepUseCase.execute(processId, dto);
    return new HttpResponse({ data: step, message: 'Flow process step created' });
  }

  @Put(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step updated', FlowProcessStepDto)
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async updateFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: UpdateFlowProcessStepDto,
  ) {
    const step = await this.updateFlowProcessStepUseCase.execute(stepId, dto);
    return new HttpResponse({ data: step, message: 'Flow process step updated' });
  }

  @Patch(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step updated', FlowProcessStepDto)
  // TODO: sanitize empty
  async partialUpdateFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: PartialUpdateFlowProcessStepDto,
  ) {
    await this.updateFlowProcessStepUseCase.execute(stepId, dto);
    return new HttpResponse({ message: 'Flow process step updated' });
  }

  @Delete(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step deleted', FlowProcessStepDto)
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async deleteFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
  ) {
    const step = await this.deleteFlowProcessStepByIdUseCase.execute({ processId, stepId });
    return new HttpResponse({ data: step, message: 'Flow process step deleted' });
  }

  @Post(':stepId/move')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step moved', FlowProcessStepDto)
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async moveFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: MoveFlowProcessStepDto,
  ) {
    const step = await this.moveFlowProcessStepUseCase.execute(stepId, processId, dto);
    return new HttpResponse({ data: step, message: 'Flow process step moved' });
  }

  @Get(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step found', FlowProcessStepDto)
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async getFlowProcessStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
  ) {
    const step = await this.getFlowProcessStepByIdUseCase.execute(stepId);
    return new HttpResponse({ data: step, message: 'Flow process step found' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process steps found', [FlowProcessStepDto])
  @SanitizeResponseWithZod(z.array(FlowProcessStepSchema))
  async getFlowProcessSteps(@Param('flowId') flowId: string, @Param('processId') processId: string) {
    const steps = await this.getAllFlowProcessStepsUseCase.execute({ processId: processId });
    return new HttpResponse({ data: steps, message: 'Flow process steps found' });
  }
}
