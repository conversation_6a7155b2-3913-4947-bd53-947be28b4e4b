import { Injectable, Logger } from '@nestjs/common';
import { customAlphabet } from 'nanoid';
import { EntityManager } from 'typeorm';
import { FlowProcessStep } from '@core/database';
import { FlowProcessStepModel } from '@core/models';

const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
const nanoid = customAlphabet(alphabet, 6);

@Injectable()
export class FlowProcessStepsService {
  private readonly logger = new Logger(FlowProcessStepsService.name);

  constructor() {}

  public normalizeEntityValues(
    entity: FlowProcessStep,
    values: Partial<FlowProcessStep | FlowProcessStepModel>,
  ): FlowProcessStep {
    Object.assign(entity, values);
    if (entity.description === '') entity.description = null;
    return entity;
  }

  private generateMaterializedPath(parentMPath?: string): string {
    const stepPath = nanoid();
    return parentMPath ? parentMPath + '/' + stepPath : stepPath;
  }

  /**
   * Potential issue with recursion.
   */
  public async generateUniqueMaterializedPath(
    entityManager: EntityManager,
    params: {
      processId: EntityId;
      parentMPath?: string;
    },
  ): Promise<string> {
    const { processId, parentMPath } = params;
    this.logger.verbose({ msg: 'Generating mPath (recursively)', data: params });

    const mPath = this.generateMaterializedPath(parentMPath);

    this.logger.verbose({ msg: `Checking if mPath (${mPath}) already exists` });

    const existingStepWithSamePath = await entityManager.findOne(FlowProcessStep, {
      where: { flowProcess: { id: processId }, mPath },
    });

    if (existingStepWithSamePath) {
      this.logger.warn({ msg: `mPath (${mPath}) already exists, generating new mPath` });
      return await this.generateUniqueMaterializedPath(entityManager, { processId, parentMPath });
    } else {
      this.logger.verbose({ msg: `mPath (${mPath}) is unique` });
    }

    return mPath;
  }
}
