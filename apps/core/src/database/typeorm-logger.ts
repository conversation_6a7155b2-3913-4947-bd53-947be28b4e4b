import { Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { Logger as TypeOrmLogger, QueryRunner } from 'typeorm';

@Injectable()
export class PinoTypeOrmLogger implements TypeOrmLogger {
  constructor(private readonly logger: Logger) {}

  /**
   * Logs query and parameters used in it.
   */
  logQuery(query: string, parameters?: unknown[], queryRunner?: QueryRunner) {
    this.logger.debug({
      msg: 'TypeORM Query',
      query: this.formatQuery(query),
      parameters,
      data: queryRunner?.data,
    });
  }

  /**
   * Logs query that is failed.
   */
  logQueryError(error: string | Error, query: string, parameters?: unknown[], queryRunner?: QueryRunner) {
    this.logger.error({
      msg: 'TypeORM Query Error',
      error: error instanceof Error ? error.message : error,
      query: this.formatQuery(query),
      parameters,
      data: queryRunner?.data,
      stack: error instanceof Error ? error.stack : undefined,
    });
  }

  /**
   * Logs query that is slow.
   */
  logQuerySlow(time: number, query: string, parameters?: unknown[], queryRunner?: QueryRunner) {
    this.logger.warn({
      msg: 'TypeORM Slow Query',
      time,
      query: this.formatQuery(query),
      parameters,
      data: queryRunner?.data,
    });
  }

  /**
   * Logs events from the schema build process.
   */
  logSchemaBuild(message: string) {
    this.logger.log({
      msg: 'TypeORM Schema Build',
      message,
    });
  }

  /**
   * Logs events from the migration run process.
   */
  logMigration(message: string) {
    this.logger.log({
      msg: 'TypeORM Migration',
      message,
    });
  }

  /**
   * Perform logging using given logger, or by default to the console.
   * Log has its own level and message.
   */
  log(level: 'log' | 'info' | 'warn', message: unknown) {
    switch (level) {
      case 'log':
      case 'info':
        this.logger.log({
          msg: 'TypeORM Log',
          message,
        });
        break;
      case 'warn':
        this.logger.warn({
          msg: 'TypeORM Warning',
          message,
        });
        break;
    }
  }

  /**
   * Format SQL query for better readability
   */
  private formatQuery(query: string): string {
    return query.replace(/\s+/g, ' ').trim();
  }
}
