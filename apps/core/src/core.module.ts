import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { LoggerModule } from 'nestjs-pino';
import { ZodValidationPipe } from 'nestjs-zod';
import { randomUUID } from 'node:crypto';
import { GlobalApiExceptionFilter, ZodResponseInterceptor } from '@libs/common/api';
import { EnvsModule, includeAppEnvFilePaths } from '@libs/common/envs';
import { EnvironmentVariables, EnvVariablesSchema } from './config';
import { FlowProcessStepsModule } from './modules/flows__flow-process-steps/flow-process-steps.module';
import { FlowProcessesModule } from './modules/flows__flow-processes/flow-processes.module';
import { FlowsModule } from './modules/flows__flows/flows.module';

@Module({
  imports: [
    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => ({
        pinoHttp: {
          level: config.get('LOG_LEVEL'),
          genReqId: request => request.headers['x-correlation-id'] || randomUUID(),
          transport: {
            target: 'pino-pretty',
            options: {
              translateTime: 'SYS:standard',
              colorize: true,
              singleLine: true,
              ignore:
                'pid,hostname,context,req.headers,req.params,req.query,req.remoteAddress,req.remotePort,res',
            },
          },
        },
      }),
    }),

    EnvsModule.forRoot({
      schema: EnvVariablesSchema,
      envFilePath: includeAppEnvFilePaths('core'),
    }),

    FlowsModule,
    FlowProcessesModule,
    FlowProcessStepsModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodResponseInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],
})
export class CoreModule {}
